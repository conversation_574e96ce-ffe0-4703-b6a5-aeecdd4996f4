// 监听 AutoDL GPU 资源 API
// 用于检查 CPU 资源的可用情况并通知用户

// 默认API地址
const DEFAULT_API_URL = "https://www.autodl.com/api/v1/machine/region/gpu_type";

const CPU_ID = "vGPU-32GB"

// 配置参数
type MonitorConfig = {
	apiUrl: string;            // API URL
	regionSignList: string[];  // 区域标识列表
	intervalSeconds: number;   // 检查间隔时间（秒）
	authCookie?: string;       // 认证Cookie
	Authorization?: string;    // 认证Token
	onAvailabilityChange?: (data: CPUAvailabilityData) => void; // 当可用性变化时的回调函数
	logToConsole: boolean;     // 是否在控制台输出日志
	debug: boolean;            // 是否启用调试模式
	mockData?: boolean;        // 是否使用模拟数据（用于测试）
	targetResource?: string;   // 要监控的资源类型，默认为 "CPU"
	barkKey?: string;          // Bark推送的设备Key
	barkUrl?: string;          // Bark服务器地址，默认为 https://api.day.app
	barkSound?: string;        // Bark通知声音
	barkLevel?: string;        // Bark通知级别
};

// 资源信息类型
type ResourceInfo = {
	idle_gpu_num: number;
	total_gpu_num: number;
};

// API 响应数据类型 (成功情况)
type ApiSuccessResponse = Array<Record<string, ResourceInfo>>;

// API 响应数据类型 (认证失败)
type ApiAuthFailedResponse = {
	code: string;
	msg: string;
	data: null;
};

// API 响应数据类型 (联合类型)
type ApiResponse = ApiSuccessResponse | ApiAuthFailedResponse;

// 简化后的可用性数据结构（用于回调函数）
type CPUAvailabilityData = {
	timestamp: number;
	isAvailable: boolean;
	idleCount: number;
	totalCount: number;
	resourceType: string;
	config?: MonitorConfig;  // 添加配置参数
};

// 上次检查的数据，用于比较变化
let lastAvailabilityData: CPUAvailabilityData | null = null;

/**
 * 发送Bark通知
 * @param title 通知标题
 * @param body 通知内容
 * @param config 监控配置
 * @param level 通知级别(可选)
 */
async function sendBarkNotification(
	title: string,
	body: string,
	config: MonitorConfig,
	level?: string
): Promise<void> {
	if (!config.barkKey) {
		return; // 如果没有设置Bark Key则不发送通知
	}

	try {
		const barkUrl = config.barkUrl || 'https://api.day.app';
		const url = new URL(`${barkUrl}/${config.barkKey}/${encodeURIComponent(title)}/${encodeURIComponent(body)}`);

		// 添加参数
		if (level || config.barkLevel) {
			url.searchParams.append('level', level || config.barkLevel || 'active');
		}
		if (config.barkSound) {
			url.searchParams.append('sound', config.barkSound);
		}

		// 添加时间戳避免缓存
		url.searchParams.append('t', Date.now().toString());

		const response = await fetch(url.toString());

		if (config.debug) {
			console.log(`[Bark通知] 发送结果: ${response.status} ${response.statusText}`);
			const responseData = await response.json();
			console.log(responseData);
		}
	} catch (error) {
		console.error(`[Bark通知] 发送失败: ${error instanceof Error ? error.message : String(error)}`);
	}
}

/**
 * 开始监听 CPU 资源
 * @param config 监听配置
 * @returns 用于停止监听的函数
 */
export function startMonitoring(config: Partial<MonitorConfig> = {}): () => void {
	// 默认配置
	const defaultConfig: MonitorConfig = {
		apiUrl: DEFAULT_API_URL,
		regionSignList: ["west-B", "west-C"], // 默认区域
		intervalSeconds: 60, // 默认每分钟检查一次
		logToConsole: true,
		debug: false,
		mockData: false,
		targetResource: "CPU", // 默认监控 CPU 资源
		barkUrl: "https://api.day.app", // 默认Bark服务器
		barkSound: "minuet", // 默认声音
		barkLevel: "active", // 默认通知级别
	};

	// 合并配置
	const finalConfig: MonitorConfig = {...defaultConfig, ...config};

	// 检查间隔不能太短，避免对 API 发起过多请求
	if (finalConfig.intervalSeconds < 3) {
		console.warn("警告: 检查间隔过短可能会导致 API 限流，已自动调整为 3 秒");
		finalConfig.intervalSeconds = 3;
	}

	// 日志函数
	const log = (message: string) => {
		if (finalConfig.logToConsole) {
			console.log(`[${finalConfig.targetResource} 监控 ${new Date().toLocaleString()}] ${message}`);
		}
	};

	// 调试日志函数
	const debug = (message: string, data?: unknown) => {
		if (finalConfig.debug && finalConfig.logToConsole) {
			console.log(`[调试 ${new Date().toLocaleString()}] ${message}`);
			if (data) {
				console.log(data);
			}
		}
	};

	// 错误日志函数
	const logError = (message: string) => {
		if (finalConfig.logToConsole) {
			console.error(`[${finalConfig.targetResource} 监控错误 ${new Date().toLocaleString()}] ${message}`);
		}
	};

	log(`开始监控 ${finalConfig.targetResource} 资源...`);
	debug(`使用 API URL: ${finalConfig.apiUrl}`);
	debug(`监控区域: ${finalConfig.regionSignList.join(", ")}`);
	debug(`监控资源: ${finalConfig.targetResource}`);
	debug(`检查间隔: ${finalConfig.intervalSeconds} 秒`);
	debug(`认证状态: ${finalConfig.authCookie ? "已配置" : "未配置"}`);
	debug(`认证Token: ${finalConfig.Authorization ? "已配置" : "未配置"}`);
	debug(`模拟数据: ${finalConfig.mockData ? "已启用" : "已禁用"}`);

	// 检查逻辑
	const checkAvailability = async () => {
		try {
			let data: ApiResponse;

			// 如果启用了模拟数据，则生成模拟数据而不是请求API
			if (finalConfig.mockData) {
				debug("使用模拟数据...");

				// 每3次请求中有1次返回有空闲资源，用于测试通知功能
				const hasCpuResource = Math.random() < 0.3;

				// 模拟一个对象数组形式的响应
				data = [
					{"vGPU-32GB": {idle_gpu_num: 281, total_gpu_num: 1420}},
					{"RTX 4090": {idle_gpu_num: 7, total_gpu_num: 2295}},
					{
						"CPU": {
							idle_gpu_num: hasCpuResource ? Math.floor(Math.random() * 5) + 1 : 0,
							total_gpu_num: 260
						}
					}
				];

				debug("生成的模拟数据:", data);
			} else {
				// 正常请求API
				debug("开始发送 API 请求...");

				// 构建请求体
				const requestBody = {
					region_sign_list: finalConfig.regionSignList
				};

				debug("请求体:", requestBody);

				// 准备请求头
				const headers: Record<string, string> = {
					"Content-Type": "application/json",
					"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
				};

				// 如果提供了认证Cookie，则添加到头部
				if (finalConfig.authCookie) {
					headers["Cookie"] = finalConfig.authCookie;
				}
				if (finalConfig.Authorization) {
					headers["Authorization"] = finalConfig.Authorization;
				}

				// 查询特定机器的信息
				const machineInfo = await fetchMachineInfo(finalConfig);
				if (machineInfo) {
					const {machine_alias, gpu_idle_num, gpu_number} = machineInfo;
					log(`机器别名: ${machine_alias}, 空闲量: ${gpu_idle_num}, 总量: ${gpu_number}`);

					// 如果空闲量大于0，发送Bark通知
					if (gpu_idle_num > 5) {
						sendBarkNotification(
							`${machine_alias}资源可用`,
							`空闲数量: ${gpu_idle_num}/${gpu_number}`,
							finalConfig!
						);
						log(`发送通知: ${machine_alias}资源可用`);
					}
				}
				return;

				// 发送 API 请求 (使用 POST 方法)
				const response = await fetch(finalConfig.apiUrl, {
					method: "POST",
					headers,
					body: JSON.stringify(requestBody)
				});

				debug(`收到 API 响应，状态码: ${response.status}`);

				if (!response.ok) {
					throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
				}

				// 获取响应文本用于调试
				const responseText = await response.text();
				debug("API 原始响应:", responseText);

				// 尝试解析 JSON
				try {
					data = JSON.parse(responseText);
					debug("解析后的数据:", data);
				} catch (e) {
					throw new Error(`无法解析 API 响应为 JSON: ${e instanceof Error ? e.message : String(e)}\n原始响应: ${responseText}`);
				}
			}

			// 验证数据结构并处理数据
			if (!data) {
				throw new Error("无法获取数据");
			}


			// 处理成功的数组响应
			// 从数组中找到目标资源项
			const targetItem = data?.data.find(item => finalConfig.targetResource! in item);

			if (!targetItem) {
				throw new Error(`找不到 ${finalConfig.targetResource} 资源信息`);
			}

			// 获取目标资源的信息
			const resourceInfo: ResourceInfo = targetItem[finalConfig.targetResource!];

			if (!resourceInfo || typeof resourceInfo.idle_gpu_num !== 'number' || typeof resourceInfo.total_gpu_num !== 'number') {
				throw new Error(`${finalConfig.targetResource} 资源信息格式不正确: ${JSON.stringify(targetItem)}`);
			}

			// 处理数据
			const currentData: CPUAvailabilityData = {
				timestamp: Date.now(),
				isAvailable: resourceInfo.idle_gpu_num > 0,
				idleCount: resourceInfo.idle_gpu_num,
				totalCount: resourceInfo.total_gpu_num,
				resourceType: finalConfig.targetResource!,
				config: finalConfig  // 将配置传递给数据对象
			};

			debug("处理后的数据:", currentData);

			// 打印当前可用资源
			if (currentData.isAvailable) {
				log(`${finalConfig.targetResource} 资源可用! 空闲数量: ${currentData.idleCount}/${currentData.totalCount}`);
			} else {
				log(`${finalConfig.targetResource} 资源不可用，空闲数量: ${currentData.idleCount}/${currentData.totalCount}`);
			}
		} catch (error) {
			logError(`监控过程中出错: ${error instanceof Error ? error.message : String(error)}`);
		}
	};

	// 立即执行一次检查
	checkAvailability();

	// 设置定时器定期检查
	const intervalId = setInterval(checkAvailability, finalConfig.intervalSeconds * 1000);

	// 返回停止监控的函数
	return () => {
		clearInterval(intervalId);
		log(`已停止监控 ${finalConfig.targetResource} 资源`);
	};
}

// 新增函数：查询特定机器的信息
async function fetchMachineInfo(config: MonitorConfig): Promise<{
	machine_alias: string;
	gpu_idle_num: number;
	gpu_number: number
} | null> {
	const url = 'https://www.autodl.com/api/v1/user/machine/list';
	const headers = {
		'accept': '*/*',
		'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
		'appversion': 'v5.72.0',
		'authorization': config.Authorization || '',
		'content-type': 'application/json;charset=UTF-8',
		'cookie': config.authCookie || '',
		'dnt': '1',
		'origin': 'https://www.autodl.com',
		'referer': 'https://www.autodl.com/market/list?page_index=3',
		'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0'
	};

	const requestBody = {
		"charge_type": "payg",
		"region_sign": "",
		"gpu_type_name": ["vGPU-32GB"],
		"machine_tag_name": [],
		"gpu_idle_num": 1,
		"mount_net_disk": false,
		"instance_disk_size_order": "",
		"date_range": "",
		"date_from": "",
		"date_to": "",
		"page_index": 1,
		"page_size": 150,
		"pay_price_order": "",
		"gpu_idle_type": "",
		"default_order": true,
		"region_sign_list": ["west-X"]
	};

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers,
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(`请求失败: ${response.status} ${response.statusText}`);
		}

		const data = await response.json();
		const machine = data.data.list.find((item: any) => item.machine_id === "dddb4fbc41");

		if (machine) {
			return {
				machine_alias: machine.machine_alias,
				gpu_idle_num: machine.gpu_idle_num,
				gpu_number: machine.gpu_number
			};
		}
	} catch (error) {
		console.error(`获取机器信息失败: ${error instanceof Error ? error.message : String(error)}`);
	}

	return null;
}

// 示例用法
if (import.meta.main) {
	console.log("=== AutoDL 资源监控 ===");
	console.log("此脚本用于监控 AutoDL 平台上资源的可用性");
	console.log("按 Ctrl+C 停止监控\n");

	// 开始监控资源
	const stopMonitoring = startMonitoring({
		// apiUrl: "自定义API地址", // 如需自定义API地址
		regionSignList: ["west-B", "west-C"], // 监控的区域
		authCookie: "_ga=GA1.1.**********.**********; HMACCOUNT=E1A4A9A7D307DFCD; Hm_lvt_e24036f31c6b8d171ce550a059a6f6fd=**********,**********,**********; _ga_NDC1CJB7XZ=GS1.1.**********.58.1.**********.0.0.0; Hm_lpvt_e24036f31c6b8d171ce550a059a6f6fd=**********", // AutoDL的认证Cookie
		Authorization: "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		targetResource: "CPU", // 监控的资源类型，可以是 "CPU", "RTX 4090", "H800" 等
		intervalSeconds: 5,  // 每 30 秒检查一次
		barkKey: "ipcJ2gi8vhpoJ4kVqsMU8E", // 替换为你的Bark Key
		barkLevel: "critical", // 重要警告
		barkSound: "minuet", // 通知声音
		onAvailabilityChange: (data) => {
			if (data.isAvailable) {
				console.log(`🎉 ${data.resourceType} 资源现在可用了！空闲数量: ${data.idleCount}`);
				// 发送Bark通知
				// sendBarkNotification(
				//   `${data.resourceType}资源可用`,
				//   `空闲数量: ${data.idleCount}/${data.totalCount}`,
				//   data.config!
				// );
			}
		},
		logToConsole: true,
		debug: false,      // 启用调试模式以查看详细信息
		mockData: false,   // 启用模拟数据（用于测试）
	});

	// 示例：运行 10 分钟后停止监控
	// setTimeout(stopMonitoring, 10 * 60 * 1000);
}
